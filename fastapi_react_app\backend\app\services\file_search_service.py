"""
檔案搜尋服務
提供檔案索引查詢功能
"""

import json
import zipfile
import rarfile
import tempfile
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.core.config import settings
from app.models.schemas import FileSearchResult, FileSearchResponse


class FileSearchService:
    """檔案搜尋服務類別"""
    
    def __init__(self):
        self.data_dir = settings.BPM_PATH_DIR
    
    def get_versions(self) -> List[str]:
        """獲取所有可用的版本"""
        if not self.data_dir.exists():
            return []
        
        index_files = list(self.data_dir.glob("*_index.json"))
        versions = [f.stem.replace("_index", "") for f in index_files]
        return sorted(versions, reverse=True)
    
    def load_index(self, version: str) -> Dict[str, Any]:
        """載入指定版本的索引檔案"""
        index_file = self.data_dir / f"{version}_index.json"
        
        if not index_file.exists():
            raise FileNotFoundError(f"Index file not found for version: {version}")
        
        with open(index_file, "r", encoding="utf-8") as f:
            return json.load(f)
    
    def query_index(self, index: Dict[str, Any], keyword: str, exact_match: bool = False) -> Dict[str, Any]:
        """在索引中搜尋包含關鍵字的項目"""
        if exact_match:
            # 精確匹配
            matches = {}
            for k, v in index.items():
                last_part = k.split('.')[-1]
                if keyword.lower() == last_part.lower():
                    matches[k] = v
        else:
            # 模糊匹配
            matches = {k: v for k, v in index.items() if keyword.lower() in k.lower()}
        
        return matches
    
    def format_datetime(self, timestamp: float) -> str:
        """格式化時間戳"""
        try:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime("%Y/%m/%d %H:%M:%S")
        except:
            return str(timestamp)
    
    def search_files(self, version: str, query: str, exact_match: bool = False) -> FileSearchResponse:
        """搜尋檔案"""
        try:
            # 載入索引資料
            index_data = self.load_index(version)
            
            # 執行查詢
            matches = self.query_index(index_data, query, exact_match)
            
            # 轉換為回應格式
            results = []
            for filename, items in matches.items():
                for item in items:
                    result = FileSearchResult(
                        filename=filename,
                        path=item.get('path_in_archive', ''),
                        modified_time=self.format_datetime(item.get('modified_time', 0)),
                        file_type=item.get('source_type', 'unknown')
                    )
                    results.append(result)
            
            return FileSearchResponse(
                results=results,
                total_count=len(results),
                query=query
            )
            
        except FileNotFoundError as e:
            raise e
        except Exception as e:
            raise Exception(f"Search failed: {str(e)}")
    
    def extract_files_from_archive(self, file_content: bytes, filename: str) -> List[str]:
        """從壓縮檔案中提取相關檔案名稱"""
        result = set()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            temp_path = Path(tmpdir) / filename
            temp_path.write_bytes(file_content)
            
            try:
                if filename.endswith(".zip"):
                    with zipfile.ZipFile(temp_path, 'r') as zf:
                        for name in zf.namelist():
                            if name.endswith(('.class', '.jsp', '.js')):
                                base = os.path.basename(name)
                                base = base.replace(".class", "").replace(".jsp", "").replace(".js", "")
                                if base:
                                    result.add(base)
                
                elif filename.endswith(".rar"):
                    with rarfile.RarFile(temp_path, 'r') as rf:
                        for info in rf.infolist():
                            if info.filename.endswith(('.class', '.jsp', '.js')):
                                base = os.path.basename(info.filename)
                                base = base.replace(".class", "").replace(".jsp", "").replace(".js", "")
                                if base:
                                    result.add(base)
                                    
            except Exception as e:
                raise Exception(f"Failed to extract files from archive: {str(e)}")
        
        return list(result)
    
    def batch_search_files(self, version: str, filenames: List[str]) -> FileSearchResponse:
        """批量搜尋檔案（精確匹配）"""
        try:
            index_data = self.load_index(version)
            
            all_matches = {}
            matched_files = []
            unmatched_files = []
            
            for filename in filenames:
                matches = self.query_index(index_data, filename, exact_match=True)
                if matches:
                    all_matches.update(matches)
                    matched_files.append(filename)
                else:
                    unmatched_files.append(filename)
            
            # 轉換為回應格式
            results = []
            for filename, items in all_matches.items():
                for item in items:
                    result = FileSearchResult(
                        filename=filename,
                        path=item.get('path_in_archive', ''),
                        modified_time=self.format_datetime(item.get('modified_time', 0)),
                        file_type=item.get('source_type', 'unknown')
                    )
                    results.append(result)
            
            return FileSearchResponse(
                results=results,
                total_count=len(results),
                query=f"Batch search: {len(matched_files)} matched, {len(unmatched_files)} unmatched"
            )
            
        except Exception as e:
            raise Exception(f"Batch search failed: {str(e)}")


# 建立服務實例
file_search_service = FileSearchService()
