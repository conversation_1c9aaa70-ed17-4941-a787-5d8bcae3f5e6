@echo off
setlocal enabledelayedexpansion
title BPM Easy Tools - FastAPI + React - Environment Setup

echo.
echo ==========================================
echo    BPM Easy Tools - FastAPI + React
echo    Environment Setup
echo ==========================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8 or higher
    echo Download URL: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 檢查 Node.js 是否安裝
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js not found, please install Node.js 16 or higher
    echo Download URL: https://nodejs.org/
    pause
    exit /b 1
)

echo Setting up environment...
echo.

REM 建立後端虛擬環境
echo [1/4] Creating backend virtual environment...
cd backend
if exist "venv" (
    echo Virtual environment already exists, skipping...
) else (
    python -m venv venv
    echo Backend virtual environment created.
)

REM 安裝後端依賴
echo [2/4] Installing backend dependencies...
call venv\Scripts\activate.bat
pip install --upgrade pip
pip install -r requirements.txt
echo Backend dependencies installed.

REM 安裝前端依賴
echo [3/4] Installing frontend dependencies...
cd ..\frontend
if exist "node_modules" (
    echo Node modules already exist, updating...
    npm update
) else (
    npm install
)
echo Frontend dependencies installed.

REM 建立資料目錄軟連結
echo [4/4] Setting up data directory...
cd ..
if not exist "data_output" (
    if exist "..\data_output" (
        echo Creating data_output symlink...
        mklink /D "data_output" "..\data_output"
        if errorlevel 1 (
            echo Warning: Failed to create symlink, copying data directory...
            xcopy "..\data_output" "data_output" /E /I /Q
        )
    ) else (
        echo Warning: Parent data_output directory not found.
        echo Creating empty data_output directory...
        mkdir data_output
        mkdir data_output\bpm_path
        mkdir data_output\bpm_release
    )
) else (
    echo Data directory already exists.
)

echo.
echo ==========================================
echo   Environment Setup Complete!
echo ==========================================
echo.
echo Next steps:
echo 1. Run 'start_application.cmd' to start the application
echo 2. Access the application at http://localhost:3000
echo 3. API documentation available at http://localhost:8000/docs
echo.
echo Press any key to exit...
pause >nul
