"""
BPM Easy Tools - FastAPI Backend
主要應用程式入口點
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path
import sys

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.api.routes import file_search, release_query
from app.core.config import settings

# 建立 FastAPI 應用實例
app = FastAPI(
    title="BPM Easy Tools API",
    description="BPM服務部好用工具 - FastAPI 後端 API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 設定 CORS 中介軟體
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React 開發伺服器
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 註冊 API 路由
app.include_router(
    file_search.router,
    prefix="/api/file-search",
    tags=["檔案索引查詢"]
)

app.include_router(
    release_query.router,
    prefix="/api/release-query",
    tags=["Release記錄查詢"]
)

@app.get("/")
async def root():
    """根路徑 - API 狀態檢查"""
    return {
        "message": "BPM Easy Tools API",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
