// 檔案搜尋相關類型
export interface FileSearchResult {
  filename: string;
  path: string;
  size?: number;
  modified_time?: string;
  file_type: string;
}

export interface FileSearchResponse {
  results: FileSearchResult[];
  total_count: number;
  query: string;
}

// Release 查詢相關類型
export interface ReleaseRecord {
  commit_hash: string;
  commit_message: string;
  author: string;
  date: string;
  branch: string;
  project: string;
  files?: string[];
}

export interface ReleaseQueryResponse {
  records: ReleaseRecord[];
  total_count: number;
  filters: Record<string, any>;
}

// 通用 API 回應類型
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface ErrorResponse {
  success: boolean;
  error: string;
  detail?: string;
}

// 檔案資訊類型
export interface FileInfo {
  project: string;
  filename: string;
  versions: string[];
  main_version: string;
  branch_type: string;
  display_name: string;
  full_name: string;
}
