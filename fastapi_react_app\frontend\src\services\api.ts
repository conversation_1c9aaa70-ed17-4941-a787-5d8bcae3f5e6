import axios from 'axios';

// 建立 axios 實例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api',
  timeout: 30000,
});

// 請求攔截器
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 回應攔截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 檔案搜尋 API
export const fileSearchAPI = {
  // 獲取版本列表
  getVersions: () => api.get<string[]>('/file-search/versions'),
  
  // 搜尋檔案
  searchFiles: (version: string, query: string, exactMatch: boolean = false) =>
    api.get('/file-search/search', {
      params: { version, query, exact_match: exactMatch }
    }),
  
  // 上傳檔案搜尋
  uploadAndSearch: (version: string, file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post(`/file-search/upload-search?version=${version}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 獲取統計資訊
  getStatistics: (version: string) => api.get(`/file-search/statistics/${version}`),
};

// Release 查詢 API
export const releaseQueryAPI = {
  // 獲取專案列表
  getProjects: () => api.get<string[]>('/release-query/projects'),
  
  // 獲取分支類型
  getBranchTypes: (project: string) =>
    api.get<string[]>(`/release-query/projects/${project}/branch-types`),
  
  // 獲取分支資料
  getBranchData: (project: string, branchType: string) =>
    api.get(`/release-query/projects/${project}/branches/${branchType}`),
  
  // 關鍵字搜尋
  searchByKeyword: (project: string, keyword: string) =>
    api.get('/release-query/search/keyword', {
      params: { project, keyword }
    }),
  
  // 檔案名稱搜尋
  searchByFilename: (project: string, filename: string) =>
    api.get('/release-query/search/filename', {
      params: { project, filename }
    }),
  
  // 獲取詳細資料
  getReleaseDetail: (filename: string) =>
    api.get(`/release-query/detail/${filename}`),
  
  // 綜合搜尋
  search: (params: {
    project?: string;
    branch?: string;
    keyword?: string;
    filename?: string;
  }) => api.post('/release-query/search', params),
};

export default api;
