"""
應用程式配置設定
"""

from pathlib import Path
from typing import Optional
import os

class Settings:
    """應用程式設定類別"""
    
    # 基本設定
    APP_NAME: str = "BPM Easy Tools API"
    VERSION: str = "2.0.0"
    DEBUG: bool = True
    
    # 伺服器設定
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 專案路徑設定
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent.parent.parent
    DATA_OUTPUT_DIR: Path = PROJECT_ROOT / "data_output"
    BPM_PATH_DIR: Path = DATA_OUTPUT_DIR / "bpm_path"
    BPM_RELEASE_DIR: Path = DATA_OUTPUT_DIR / "bpm_release"
    
    # CORS 設定
    ALLOWED_ORIGINS: list = [
        "http://localhost:3000",
        "http://127.0.0.1:3000"
    ]
    
    def __init__(self):
        """初始化設定，檢查必要目錄"""
        self._ensure_directories()
    
    def _ensure_directories(self):
        """確保必要的目錄存在"""
        directories = [
            self.DATA_OUTPUT_DIR,
            self.BPM_PATH_DIR,
            self.BPM_RELEASE_DIR
        ]
        
        for directory in directories:
            if not directory.exists():
                print(f"Warning: Directory not found: {directory}")

# 建立全域設定實例
settings = Settings()
