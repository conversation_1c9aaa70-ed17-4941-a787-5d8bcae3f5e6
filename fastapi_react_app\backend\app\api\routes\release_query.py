"""
Release 查詢 API 路由
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional, Dict, Any

from app.models.schemas import (
    ReleaseQueryRequest,
    ReleaseQueryResponse,
    APIResponse,
    ErrorResponse
)
from app.services.release_query_service import release_query_service

router = APIRouter()


@router.get("/projects", response_model=List[str])
async def get_projects():
    """獲取所有專案名稱"""
    try:
        projects = release_query_service.get_projects()
        return projects
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/projects/{project}/branch-types", response_model=List[str])
async def get_branch_types(project: str):
    """獲取指定專案的分支類型"""
    try:
        branch_types = release_query_service.get_branch_types(project)
        return branch_types
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/projects/{project}/branches/{branch_type}")
async def get_branch_data(project: str, branch_type: str):
    """獲取指定專案和分支類型的資料"""
    try:
        branch_data = release_query_service.get_branch_data(project, branch_type)
        return APIResponse(
            success=True,
            message="Branch data retrieved successfully",
            data=branch_data
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search/keyword", response_model=ReleaseQueryResponse)
async def search_by_keyword(
    project: str = Query(..., description="專案名稱"),
    keyword: str = Query(..., description="搜尋關鍵字")
):
    """根據關鍵字搜尋 commit 訊息"""
    try:
        result = release_query_service.search_by_keyword(project, keyword)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search/filename", response_model=ReleaseQueryResponse)
async def search_by_filename(
    project: str = Query(..., description="專案名稱"),
    filename: str = Query(..., description="檔案名稱")
):
    """根據檔案名稱搜尋"""
    try:
        result = release_query_service.search_by_filename(project, filename)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/detail/{filename}")
async def get_release_detail(filename: str):
    """獲取指定檔案的詳細 release 資料"""
    try:
        detail = release_query_service.get_release_detail(filename)
        if detail is None:
            raise HTTPException(status_code=404, detail="Release file not found")
        
        return APIResponse(
            success=True,
            message="Release detail retrieved successfully",
            data=detail
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search", response_model=ReleaseQueryResponse)
async def search_releases(request: ReleaseQueryRequest):
    """綜合搜尋 release 記錄"""
    try:
        if request.keyword:
            result = release_query_service.search_by_keyword(
                request.project or "", request.keyword
            )
        elif request.filename:
            result = release_query_service.search_by_filename(
                request.project or "", request.filename
            )
        else:
            # 如果沒有指定搜尋條件，返回空結果
            result = ReleaseQueryResponse(
                records=[],
                total_count=0,
                filters=request.dict()
            )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
