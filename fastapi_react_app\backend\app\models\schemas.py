"""
Pydantic 資料模型定義
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# 檔案搜尋相關模型
class FileSearchRequest(BaseModel):
    """檔案搜尋請求模型"""
    query: str = Field(..., description="搜尋關鍵字")
    file_types: Optional[List[str]] = Field(default=None, description="檔案類型過濾")
    
class FileSearchResult(BaseModel):
    """檔案搜尋結果模型"""
    filename: str = Field(..., description="檔案名稱")
    path: str = Field(..., description="檔案路徑")
    size: Optional[int] = Field(default=None, description="檔案大小")
    modified_time: Optional[str] = Field(default=None, description="修改時間")
    file_type: str = Field(..., description="檔案類型")

class FileSearchResponse(BaseModel):
    """檔案搜尋回應模型"""
    results: List[FileSearchResult] = Field(..., description="搜尋結果")
    total_count: int = Field(..., description="總結果數量")
    query: str = Field(..., description="搜尋關鍵字")

# Release 查詢相關模型
class ReleaseQueryRequest(BaseModel):
    """Release 查詢請求模型"""
    project: Optional[str] = Field(default=None, description="專案名稱")
    branch: Optional[str] = Field(default=None, description="分支名稱")
    keyword: Optional[str] = Field(default=None, description="關鍵字搜尋")
    filename: Optional[str] = Field(default=None, description="檔案名稱搜尋")

class ReleaseRecord(BaseModel):
    """Release 記錄模型"""
    commit_hash: str = Field(..., description="Commit Hash")
    commit_message: str = Field(..., description="Commit 訊息")
    author: str = Field(..., description="作者")
    date: str = Field(..., description="提交日期")
    branch: str = Field(..., description="分支名稱")
    project: str = Field(..., description="專案名稱")
    files: Optional[List[str]] = Field(default=None, description="修改的檔案")

class ReleaseQueryResponse(BaseModel):
    """Release 查詢回應模型"""
    records: List[ReleaseRecord] = Field(..., description="Release 記錄")
    total_count: int = Field(..., description="總記錄數量")
    filters: Dict[str, Any] = Field(..., description="套用的過濾條件")

# 通用回應模型
class APIResponse(BaseModel):
    """通用 API 回應模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="回應訊息")
    data: Optional[Any] = Field(default=None, description="回應資料")

class ErrorResponse(BaseModel):
    """錯誤回應模型"""
    success: bool = Field(default=False, description="操作是否成功")
    error: str = Field(..., description="錯誤訊息")
    detail: Optional[str] = Field(default=None, description="錯誤詳細資訊")
