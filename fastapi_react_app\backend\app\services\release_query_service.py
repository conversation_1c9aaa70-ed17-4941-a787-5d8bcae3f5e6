"""
Release 查詢服務
提供 Release 記錄查詢功能
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.core.config import settings
from app.models.schemas import ReleaseRecord, ReleaseQueryResponse


class ReleaseQueryService:
    """Release 查詢服務類別"""
    
    def __init__(self):
        self.release_dir = settings.BPM_RELEASE_DIR
    
    def get_release_files(self) -> List[Path]:
        """獲取所有 release JSON 檔案"""
        if not self.release_dir.exists():
            return []
        return list(self.release_dir.glob("*.json"))
    
    def parse_filename(self, filename: Path) -> Dict[str, Any]:
        """解析檔案名稱，提取專案名稱、分支等資訊"""
        name = filename.stem
        parts = name.split('_')
        
        project = parts[0] if parts else "Unknown"
        
        # 提取版本資訊
        version_pattern = r'(\d+\.\d+\.\d+\.\d+)'
        versions = re.findall(version_pattern, name)
        
        # 判斷分支類型
        branch_type = "unknown"
        if "hotfix" in name.lower():
            branch_type = "hotfix"
        elif "release" in name.lower():
            branch_type = "release"
        
        # 提取主要版本號用於排序
        main_version = versions[0] if versions else "0.0.0.0"
        
        return {
            'project': project,
            'filename': filename.name,
            'versions': versions,
            'main_version': main_version,
            'branch_type': branch_type,
            'full_name': name
        }
    
    def load_release_data(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """載入 release JSON 資料"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return None
    
    def format_datetime_display(self, dt_str: str) -> str:
        """格式化日期時間顯示"""
        try:
            dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
            return dt.strftime("%Y/%m/%d %H:%M:%S")
        except:
            return dt_str
    
    def get_projects(self) -> List[str]:
        """獲取所有專案名稱"""
        release_files = self.get_release_files()
        file_info = [self.parse_filename(f) for f in release_files]
        projects = sorted(list(set([info['project'] for info in file_info])))
        return projects
    
    def get_project_files(self, project: str) -> List[Dict[str, Any]]:
        """獲取指定專案的所有檔案資訊"""
        release_files = self.get_release_files()
        file_info = [self.parse_filename(f) for f in release_files]
        return [info for info in file_info if info['project'] == project]
    
    def get_branch_types(self, project: str) -> List[str]:
        """獲取專案的分支類型"""
        project_files = self.get_project_files(project)
        branch_types = set()
        for info in project_files:
            if info['branch_type'] != "unknown":
                branch_types.add(info['branch_type'])
        return sorted(list(branch_types))
    
    def search_by_keyword(self, project: str, keyword: str) -> ReleaseQueryResponse:
        """根據關鍵字搜尋 commit 訊息"""
        project_files = self.get_project_files(project)
        results = []
        
        for info in project_files:
            file_path = self.release_dir / info['filename']
            data = self.load_release_data(file_path)
            
            if data:
                branch_name = data.get("比較資訊", {}).get("新分支", {}).get("branch_name", "Unknown")
                commits = data.get("新增commit記錄", [])
                
                for commit in commits:
                    commit_msg = commit.get('commit_訊息', '')
                    if keyword.lower() in commit_msg.lower():
                        record = ReleaseRecord(
                            commit_hash=commit.get('commit_hash', ''),
                            commit_message=commit_msg,
                            author=commit.get('作者', ''),
                            date=self.format_datetime_display(commit.get('提交日期', '')),
                            branch=branch_name,
                            project=project,
                            files=[change.get('檔案路徑', '') for change in commit.get('檔案變更', [])]
                        )
                        results.append(record)
        
        return ReleaseQueryResponse(
            records=results,
            total_count=len(results),
            filters={"project": project, "keyword": keyword}
        )
    
    def search_by_filename(self, project: str, filename: str) -> ReleaseQueryResponse:
        """根據檔案名稱搜尋"""
        project_files = self.get_project_files(project)
        results = []
        
        for info in project_files:
            file_path = self.release_dir / info['filename']
            data = self.load_release_data(file_path)
            
            if data:
                branch_name = data.get("比較資訊", {}).get("新分支", {}).get("branch_name", "Unknown")
                commits = data.get("新增commit記錄", [])
                
                for commit in commits:
                    file_changes = commit.get('檔案變更', [])
                    matching_files = []
                    
                    for change in file_changes:
                        file_path_str = change.get('檔案路徑', '')
                        if filename.lower() in file_path_str.lower():
                            matching_files.append(file_path_str)
                    
                    if matching_files:
                        record = ReleaseRecord(
                            commit_hash=commit.get('commit_hash', ''),
                            commit_message=commit.get('commit_訊息', ''),
                            author=commit.get('作者', ''),
                            date=self.format_datetime_display(commit.get('提交日期', '')),
                            branch=branch_name,
                            project=project,
                            files=matching_files
                        )
                        results.append(record)
        
        return ReleaseQueryResponse(
            records=results,
            total_count=len(results),
            filters={"project": project, "filename": filename}
        )
    
    def get_branch_data(self, project: str, branch_type: str) -> List[Dict[str, Any]]:
        """獲取指定分支類型的資料"""
        project_files = self.get_project_files(project)
        filtered_files = [info for info in project_files if info['branch_type'] == branch_type]
        
        # 按版本號排序
        filtered_files.sort(key=lambda x: self._version_sort_key(x['main_version']))
        
        return filtered_files
    
    def get_release_detail(self, filename: str) -> Optional[Dict[str, Any]]:
        """獲取指定檔案的詳細 release 資料"""
        file_path = self.release_dir / filename
        return self.load_release_data(file_path)
    
    def _version_sort_key(self, version_str: str) -> List[int]:
        """版本號排序鍵"""
        try:
            parts = [int(x) for x in version_str.split('.')]
            while len(parts) < 4:
                parts.append(0)
            return [-x for x in parts]  # 負數實現降序排序
        except:
            return [0, 0, 0, 0]


# 建立服務實例
release_query_service = ReleaseQueryService()
