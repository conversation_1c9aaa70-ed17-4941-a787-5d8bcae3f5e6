import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Container, AppBar, Toolbar, Typography, Box } from '@mui/material';
import HomePage from './pages/HomePage';
import FileSearchPage from './pages/FileSearchPage';
import ReleaseQueryPage from './pages/ReleaseQueryPage';

function App() {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static" sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
      }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            🛠️ BPM服務部好用工具
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            v2.0.0 | FastAPI + React
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/file-search" element={<FileSearchPage />} />
          <Route path="/release-query" element={<ReleaseQueryPage />} />
        </Routes>
      </Container>

      <Box 
        component="footer" 
        sx={{ 
          mt: 8, 
          py: 3, 
          textAlign: 'center', 
          borderTop: '1px solid #eee',
          color: '#666'
        }}
      >
        <Typography variant="body2">
          🏢 BPM服務部 | 版本 2.0.0 | 最後更新：2025年9月
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          如有問題或建議，請聯繫開發團隊
        </Typography>
      </Box>
    </Box>
  );
}

export default App;
