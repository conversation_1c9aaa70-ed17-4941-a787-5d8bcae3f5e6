# BPM Easy Tools - 部署指南

本文件說明如何部署 BPM Easy Tools FastAPI + React 版本到生產環境。

## 部署選項

### 選項 1: 本地部署 (推薦用於內部使用)

#### 準備工作
1. 確保目標機器已安裝：
   - Python 3.8+
   - Node.js 16+
   - Git (可選，用於版本控制)

2. 複製專案檔案到目標機器

#### 部署步驟
1. **設定環境**：
   ```cmd
   setup_environment.cmd
   ```

2. **建立生產版本**：
   ```cmd
   cd frontend
   npm run build
   ```

3. **配置生產環境**：
   - 修改 `backend/app/core/config.py` 中的設定
   - 設定 `DEBUG = False`
   - 配置正確的 CORS 來源

4. **啟動服務**：
   ```cmd
   start_application.cmd
   ```

### 選項 2: Docker 部署

#### Dockerfile (後端)
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY backend/ .
COPY data_output/ ./data_output/

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Dockerfile (前端)
```dockerfile
FROM node:16-alpine as build

WORKDIR /app
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    volumes:
      - ./data_output:/app/data_output:ro
    environment:
      - DEBUG=false

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "80:80"
    depends_on:
      - backend
```

### 選項 3: Windows 服務部署

#### 建立 Windows 服務
1. 安裝 `pywin32`：
   ```cmd
   pip install pywin32
   ```

2. 建立服務腳本 `service.py`：
   ```python
   import win32serviceutil
   import win32service
   import win32event
   import subprocess
   import os

   class BPMToolsService(win32serviceutil.ServiceFramework):
       _svc_name_ = "BPMEasyTools"
       _svc_display_name_ = "BPM Easy Tools Service"
       
       def __init__(self, args):
           win32serviceutil.ServiceFramework.__init__(self, args)
           self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
           
       def SvcStop(self):
           self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
           win32event.SetEvent(self.hWaitStop)
           
       def SvcDoRun(self):
           # 啟動應用程式
           subprocess.Popen([
               "cmd", "/c", "start_application.cmd"
           ], cwd=os.path.dirname(__file__))
           
           win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)

   if __name__ == '__main__':
       win32serviceutil.HandleCommandLine(BPMToolsService)
   ```

3. 安裝服務：
   ```cmd
   python service.py install
   python service.py start
   ```

## 生產環境配置

### 後端配置
1. **環境變數**：
   ```bash
   export DEBUG=false
   export HOST=0.0.0.0
   export PORT=8000
   ```

2. **安全設定**：
   - 配置 HTTPS (建議使用 nginx 反向代理)
   - 設定適當的 CORS 來源
   - 啟用日誌記錄

3. **性能優化**：
   - 使用 Gunicorn 或 uWSGI 作為 WSGI 伺服器
   - 配置適當的 worker 數量
   - 啟用 gzip 壓縮

### 前端配置
1. **建置優化**：
   ```bash
   npm run build
   ```

2. **靜態檔案服務**：
   - 使用 nginx 或 Apache 服務靜態檔案
   - 配置適當的快取標頭
   - 啟用 gzip 壓縮

### Nginx 配置範例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端靜態檔案
    location / {
        root /path/to/frontend/build;
        try_files $uri $uri/ /index.html;
        
        # 快取設定
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API 文檔
    location /docs {
        proxy_pass http://localhost:8000/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 監控與維護

### 日誌管理
1. **後端日誌**：
   - 配置 Python logging
   - 使用 logrotate 管理日誌檔案

2. **前端日誌**：
   - 配置 nginx 存取日誌
   - 監控錯誤日誌

### 健康檢查
1. **API 健康檢查**：
   ```bash
   curl http://localhost:8000/health
   ```

2. **自動化監控**：
   - 使用 `test_api.py` 進行定期檢查
   - 配置 cron job 或 Windows 排程工作

### 備份策略
1. **資料備份**：
   - 定期備份 `data_output` 目錄
   - 版本控制配置檔案

2. **應用程式備份**：
   - 保留多個版本的部署檔案
   - 建立回滾計畫

## 故障排除

### 常見部署問題
1. **埠衝突**：
   - 檢查 8000 和 3000 埠是否被佔用
   - 修改配置檔案中的埠設定

2. **權限問題**：
   - 確保應用程式有讀取資料檔案的權限
   - 檢查防火牆設定

3. **依賴問題**：
   - 確認所有依賴套件已正確安裝
   - 檢查 Python 和 Node.js 版本

### 性能調優
1. **後端優化**：
   - 調整 worker 數量
   - 啟用快取機制
   - 優化資料庫查詢

2. **前端優化**：
   - 啟用程式碼分割
   - 優化圖片和資源
   - 使用 CDN

## 安全考量

1. **網路安全**：
   - 使用 HTTPS
   - 配置適當的 CORS 設定
   - 限制 API 存取來源

2. **資料安全**：
   - 定期備份重要資料
   - 限制檔案存取權限
   - 監控異常存取

3. **應用程式安全**：
   - 定期更新依賴套件
   - 進行安全性掃描
   - 實施存取控制

## 升級指南

1. **備份現有版本**
2. **測試新版本**
3. **逐步部署**
4. **驗證功能**
5. **監控系統狀態**

如有部署相關問題，請聯繫 BPM服務部開發團隊。
