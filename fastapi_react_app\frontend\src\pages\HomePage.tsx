import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Container,
  Chip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Assessment as AssessmentIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const tools = [
    {
      id: 'file-search',
      title: '檔案索引路徑查詢工具',
      description: [
        '查詢class、jsp、js檔案位置',
        '支援關鍵字模糊搜尋',
        '上傳壓縮檔案批量查詢',
        '顯示檔案修改時間',
        '多版本索引支援'
      ],
      icon: <SearchIcon sx={{ fontSize: 60 }} />,
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      route: '/file-search'
    },
    {
      id: 'release-query',
      title: '產品Release記錄查詢工具',
      description: [
        '查詢BPM、BPM-ISO、NaNaXWeb專案的release記錄',
        '依照branch_name瀏覽commit記錄',
        '關鍵字搜尋commit訊息',
        '檔案名稱搜尋功能',
        '支援排序與分類顯示'
      ],
      icon: <AssessmentIcon sx={{ fontSize: 60 }} />,
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      route: '/release-query'
    }
  ];

  return (
    <Container maxWidth="lg">
      {/* 主標題 */}
      <Box textAlign="center" mb={6}>
        <Typography
          variant="h2"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '2px 2px 4px rgba(0,0,0,0.1)',
          }}
        >
          🛠️ BPM服務部好用工具
        </Typography>
        <Typography variant="h5" color="text.secondary" gutterBottom>
          選擇您需要的工具來提升工作效率
        </Typography>
        <Chip 
          label="FastAPI + React 架構" 
          color="primary" 
          variant="outlined"
          sx={{ mt: 2 }}
        />
      </Box>

      {/* 工具卡片 */}
      <Grid container spacing={4} justifyContent="center">
        {tools.map((tool) => (
          <Grid item xs={12} md={6} key={tool.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
              }}
            >
              {/* 卡片頭部 */}
              <Box
                sx={{
                  background: tool.color,
                  color: 'white',
                  p: 3,
                  textAlign: 'center',
                  minHeight: 120,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              >
                {tool.icon}
                <Typography variant="h6" component="h2" sx={{ mt: 1, fontWeight: 'bold' }}>
                  {tool.title}
                </Typography>
              </Box>

              {/* 卡片內容 */}
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Box component="ul" sx={{ pl: 2, m: 0 }}>
                  {tool.description.map((item, index) => (
                    <Typography
                      component="li"
                      variant="body2"
                      color="text.secondary"
                      key={index}
                      sx={{ mb: 1, lineHeight: 1.6 }}
                    >
                      {item}
                    </Typography>
                  ))}
                </Box>
              </CardContent>

              {/* 卡片操作 */}
              <CardActions sx={{ p: 3, pt: 0 }}>
                <Button
                  variant="contained"
                  fullWidth
                  size="large"
                  onClick={() => navigate(tool.route)}
                  sx={{
                    background: tool.color,
                    '&:hover': {
                      background: tool.color,
                      opacity: 0.9,
                    },
                    borderRadius: 2,
                    py: 1.5,
                  }}
                >
                  進入工具
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 系統資訊 */}
      <Box
        sx={{
          mt: 6,
          p: 3,
          background: '#f8f9fa',
          borderRadius: 2,
          borderLeft: '4px solid #667eea',
        }}
      >
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
          ℹ️ 系統資訊
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" color="text.secondary">
              📦 版本：2.0.0
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" color="text.secondary">
              📅 更新：2025年9月
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" color="text.secondary">
              🏢 部門：BPM服務部
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" color="text.secondary">
              ⚡ 架構：FastAPI + React
            </Typography>
          </Grid>
        </Grid>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          💡 提示：點擊上方工具卡片進入對應功能頁面
        </Typography>
      </Box>
    </Container>
  );
};

export default HomePage;
