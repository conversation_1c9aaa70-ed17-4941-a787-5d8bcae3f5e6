{"name": "bpm-easy-tools-frontend", "version": "2.0.0", "description": "BPM Easy Tools - React Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "@mui/material": "^5.10.0", "@mui/icons-material": "^5.10.0", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@mui/lab": "^5.0.0-alpha.90"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}