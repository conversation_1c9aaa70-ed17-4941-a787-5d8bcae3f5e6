#!/usr/bin/env python3
"""
簡單的 API 測試腳本
用於驗證 FastAPI 後端是否正常運作
"""

import requests
import json
import sys
from pathlib import Path

# API 基礎 URL
BASE_URL = "http://localhost:8000"

def test_api_health():
    """測試 API 健康檢查"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API 健康檢查通過")
            return True
        else:
            print(f"❌ API 健康檢查失敗: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到 API 伺服器，請確認後端服務已啟動")
        return False

def test_file_search_versions():
    """測試檔案搜尋版本列表 API"""
    try:
        response = requests.get(f"{BASE_URL}/api/file-search/versions")
        if response.status_code == 200:
            versions = response.json()
            print(f"✅ 檔案搜尋版本列表 API 正常，找到 {len(versions)} 個版本")
            if versions:
                print(f"   版本: {', '.join(versions[:3])}{'...' if len(versions) > 3 else ''}")
            return True
        else:
            print(f"❌ 檔案搜尋版本列表 API 失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 檔案搜尋版本列表 API 測試異常: {e}")
        return False

def test_release_query_projects():
    """測試 Release 查詢專案列表 API"""
    try:
        response = requests.get(f"{BASE_URL}/api/release-query/projects")
        if response.status_code == 200:
            projects = response.json()
            print(f"✅ Release 查詢專案列表 API 正常，找到 {len(projects)} 個專案")
            if projects:
                print(f"   專案: {', '.join(projects)}")
            return True
        else:
            print(f"❌ Release 查詢專案列表 API 失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Release 查詢專案列表 API 測試異常: {e}")
        return False

def test_api_docs():
    """測試 API 文檔是否可訪問"""
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ API 文檔可正常訪問")
            return True
        else:
            print(f"❌ API 文檔訪問失敗: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API 文檔測試異常: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始測試 FastAPI 後端 API...")
    print("=" * 50)
    
    tests = [
        ("API 健康檢查", test_api_health),
        ("檔案搜尋版本列表", test_file_search_versions),
        ("Release 查詢專案列表", test_release_query_projects),
        ("API 文檔訪問", test_api_docs),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 測試: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"   提示: 請檢查 data_output 目錄是否存在相關資料檔案")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！API 後端運作正常")
        print(f"🌐 API 文檔: {BASE_URL}/docs")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查後端配置和資料檔案")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
