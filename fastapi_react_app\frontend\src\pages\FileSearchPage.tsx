import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Upload as UploadIcon,
  Description as DescriptionIcon,
  Folder as FolderIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { fileSearchAPI } from '../services/api';
import { FileSearchResponse, FileSearchResult } from '../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const FileSearchPage: React.FC = () => {
  const navigate = useNavigate();
  const [versions, setVersions] = useState<string[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [exactMatch, setExactMatch] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<FileSearchResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [tabValue, setTabValue] = useState<number>(0);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [uploadResults, setUploadResults] = useState<FileSearchResponse | null>(null);

  // 載入版本列表
  useEffect(() => {
    const loadVersions = async () => {
      try {
        const response = await fileSearchAPI.getVersions();
        setVersions(response.data);
        if (response.data.length > 0) {
          setSelectedVersion(response.data[0]);
        }
      } catch (err) {
        setError('無法載入版本列表');
        console.error('Error loading versions:', err);
      }
    };
    loadVersions();
  }, []);

  // 處理搜尋
  const handleSearch = async () => {
    if (!searchQuery.trim() || !selectedVersion) {
      setError('請輸入搜尋關鍵字並選擇版本');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await fileSearchAPI.searchFiles(selectedVersion, searchQuery, exactMatch);
      setSearchResults(response.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || '搜尋失敗');
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  // 處理檔案上傳
  const handleFileUpload = async () => {
    if (!uploadFile || !selectedVersion) {
      setError('請選擇檔案並選擇版本');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await fileSearchAPI.uploadAndSearch(selectedVersion, uploadFile);
      setUploadResults(response.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || '檔案上傳搜尋失敗');
      setUploadResults(null);
    } finally {
      setLoading(false);
    }
  };

  // 渲染搜尋結果
  const renderSearchResults = (results: FileSearchResponse) => {
    if (results.total_count === 0) {
      return (
        <Alert severity="info" sx={{ mt: 2 }}>
          找不到符合條件的資料
        </Alert>
      );
    }

    return (
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          🔎 搜尋結果 - 關鍵字：{results.query}
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          共找到 {results.total_count} 個結果
        </Typography>

        {results.results.map((result, index) => (
          <Accordion key={index} sx={{ mt: 1 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {result.filename}
                </Typography>
                <Chip
                  label={result.file_type}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ ml: 'auto' }}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ pl: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <FolderIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    路徑：{result.path}
                  </Typography>
                </Box>
                {result.modified_time && (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      最後修改：{result.modified_time}
                    </Typography>
                  </Box>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };

  return (
    <Box>
      {/* 頁面標題 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          🔍 檔案索引路徑查詢工具
        </Typography>
      </Box>

      {/* 錯誤訊息 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* 版本選擇 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            選擇版本
          </Typography>
          <FormControl fullWidth>
            <InputLabel>版本</InputLabel>
            <Select
              value={selectedVersion}
              label="版本"
              onChange={(e) => setSelectedVersion(e.target.value)}
            >
              {versions.map((version) => (
                <MenuItem key={version} value={version}>
                  {version}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </CardContent>
      </Card>

      {/* 查詢方式選擇 */}
      <Card>
        <CardContent>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="輸入檔案名稱" />
            <Tab label="上傳壓縮檔案" />
          </Tabs>

          {/* 輸入檔案名稱 */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="請輸入要查詢的 class、jsp 或 js 名稱"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="可模糊搜尋"
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={exactMatch}
                      onChange={(e) => setExactMatch(e.target.checked)}
                    />
                  }
                  label="精確匹配"
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={handleSearch}
                  disabled={loading || !searchQuery.trim() || !selectedVersion}
                  startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                >
                  {loading ? '搜尋中...' : '搜尋'}
                </Button>
              </Grid>
            </Grid>
            {searchResults && renderSearchResults(searchResults)}
          </TabPanel>

          {/* 上傳壓縮檔案 */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  startIcon={<UploadIcon />}
                >
                  選擇檔案 (ZIP/RAR)
                  <input
                    type="file"
                    hidden
                    accept=".zip,.rar"
                    onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                  />
                </Button>
                {uploadFile && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    已選擇：{uploadFile.name}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={handleFileUpload}
                  disabled={loading || !uploadFile || !selectedVersion}
                  startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                >
                  {loading ? '處理中...' : '上傳並搜尋'}
                </Button>
              </Grid>
            </Grid>
            {uploadResults && renderSearchResults(uploadResults)}
          </TabPanel>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FileSearchPage;
