"""
檔案搜尋 API 路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Query
from typing import List, Optional

from app.models.schemas import (
    FileSearchRequest, 
    FileSearchResponse, 
    APIResponse, 
    ErrorResponse
)
from app.services.file_search_service import file_search_service

router = APIRouter()


@router.get("/versions", response_model=List[str])
async def get_versions():
    """獲取所有可用的版本"""
    try:
        versions = file_search_service.get_versions()
        return versions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search", response_model=FileSearchResponse)
async def search_files(
    version: str = Query(..., description="版本號"),
    query: str = Query(..., description="搜尋關鍵字"),
    exact_match: bool = Query(False, description="是否精確匹配")
):
    """搜尋檔案"""
    try:
        result = file_search_service.search_files(version, query, exact_match)
        return result
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-search", response_model=FileSearchResponse)
async def upload_and_search(
    version: str = Query(..., description="版本號"),
    file: UploadFile = File(..., description="上傳的壓縮檔案")
):
    """上傳壓縮檔案並搜尋其中的檔案"""
    try:
        # 檢查檔案類型
        if not file.filename.endswith(('.zip', '.rar')):
            raise HTTPException(
                status_code=400, 
                detail="Only ZIP and RAR files are supported"
            )
        
        # 讀取檔案內容
        file_content = await file.read()
        
        # 提取檔案名稱
        filenames = file_search_service.extract_files_from_archive(
            file_content, file.filename
        )
        
        if not filenames:
            raise HTTPException(
                status_code=400,
                detail="No relevant files found in the archive"
            )
        
        # 批量搜尋
        result = file_search_service.batch_search_files(version, filenames)
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/{version}")
async def get_statistics(version: str):
    """獲取指定版本的統計資訊"""
    try:
        index_data = file_search_service.load_index(version)
        
        stats = {
            'total_files': len(index_data),
            'file_types': {},
            'total_instances': 0
        }
        
        file_types = {'Class檔案': 0, 'JSP檔案': 0, 'JS檔案': 0, '其他檔案': 0}
        
        for file_name, instances in index_data.items():
            stats['total_instances'] += len(instances)
            
            # 分析檔案類型
            if file_name.endswith('.class') or '.' not in file_name.split('/')[-1]:
                file_types['Class檔案'] += 1
            elif '.jsp' in file_name.lower():
                file_types['JSP檔案'] += 1
            elif '.js' in file_name.lower():
                file_types['JS檔案'] += 1
            else:
                file_types['其他檔案'] += 1
        
        stats['file_types'] = file_types
        
        return APIResponse(
            success=True,
            message="Statistics retrieved successfully",
            data=stats
        )
        
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
