# BPM Easy Tools - FastAPI + React 版本

這是 BPM Easy Tools 的 FastAPI + React 重新設計版本，保留了原有的核心功能並提供更現代化的架構。

## 功能特色

1. **檔案索引路徑查詢工具** - 查詢 class、jsp、js 檔案位置
2. **產品Release記錄查詢工具** - 查詢 BPM、BPM-ISO、NaNaXWeb 專案的 release 記錄

## 技術架構

- **後端**: FastAPI (Python 3.8+)
- **前端**: React 18 + TypeScript + Material-UI
- **資料存儲**: JSON 檔案 (與原專案相容)
- **API 文檔**: 自動生成的 OpenAPI/Swagger 文檔

## 專案結構

```
fastapi_react_app/
├── backend/                 # FastAPI 後端
│   ├── app/
│   │   ├── api/            # API 路由
│   │   │   └── routes/     # 具體路由實作
│   │   ├── core/           # 核心配置
│   │   ├── models/         # Pydantic 資料模型
│   │   └── services/       # 業務邏輯服務
│   ├── requirements.txt    # Python 依賴
│   └── main.py            # FastAPI 應用入口
├── frontend/               # React 前端
│   ├── src/
│   │   ├── pages/         # 頁面元件
│   │   ├── services/      # API 服務
│   │   ├── types/         # TypeScript 類型定義
│   │   └── App.tsx        # 主應用元件
│   ├── package.json       # Node.js 依賴
│   ├── tsconfig.json      # TypeScript 配置
│   └── public/            # 靜態資源
├── data_output/           # 資料目錄 (軟連結到原專案)
├── start_application.cmd  # 一鍵啟動腳本
├── setup_environment.cmd  # 環境設定腳本
├── test_api.py           # API 測試腳本
└── README.md             # 專案說明文件
```

## 安裝與設定

### 系統需求

- Python 3.8 或更高版本
- Node.js 16 或更高版本
- npm 或 yarn 套件管理器

### 快速開始

1. **環境設定** (首次使用)：
   ```cmd
   setup_environment.cmd
   ```

2. **啟動應用程式**：
   ```cmd
   start_application.cmd
   ```

3. **訪問應用程式**：
   - 前端應用: http://localhost:3000
   - API 後端: http://localhost:8000
   - API 文檔: http://localhost:8000/docs

### 手動安裝 (開發者)

#### 後端設定
```bash
cd backend
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
python main.py
```

#### 前端設定
```bash
cd frontend
npm install
npm start
```

## API 端點

### 檔案搜尋 API
- `GET /api/file-search/versions` - 獲取版本列表
- `GET /api/file-search/search` - 搜尋檔案
- `POST /api/file-search/upload-search` - 上傳檔案搜尋
- `GET /api/file-search/statistics/{version}` - 獲取統計資訊

### Release 查詢 API
- `GET /api/release-query/projects` - 獲取專案列表
- `GET /api/release-query/projects/{project}/branch-types` - 獲取分支類型
- `GET /api/release-query/search/keyword` - 關鍵字搜尋
- `GET /api/release-query/search/filename` - 檔案名稱搜尋
- `GET /api/release-query/detail/{filename}` - 獲取詳細資料

## 測試

### API 測試
```bash
python test_api.py
```

### 前端測試
```bash
cd frontend
npm test
```

## 開發說明

### 後端開發
- 使用 FastAPI 框架提供高性能 API
- Pydantic 模型進行資料驗證
- 自動生成 OpenAPI 文檔
- 支援異步處理
- CORS 配置支援跨域請求

### 前端開發
- React 18 + TypeScript 提供類型安全
- Material-UI 提供現代化 UI 元件
- React Router 處理路由
- Axios 處理 API 請求
- 響應式設計支援多種裝置

### 資料相容性
- 完全相容原 Streamlit 版本的資料格式
- 支援現有的 `data_output` 目錄結構
- 無需資料遷移或轉換

## 故障排除

### 常見問題

1. **無法啟動後端服務**
   - 檢查 Python 版本 (需要 3.8+)
   - 確認虛擬環境已正確建立
   - 檢查依賴套件是否完整安裝

2. **無法啟動前端服務**
   - 檢查 Node.js 版本 (需要 16+)
   - 確認 npm 依賴已安裝
   - 檢查 3000 埠是否被佔用

3. **API 無法連接**
   - 確認後端服務已啟動 (http://localhost:8000)
   - 檢查防火牆設定
   - 確認 CORS 配置正確

4. **找不到資料**
   - 確認 `data_output` 目錄存在
   - 檢查原專案的資料檔案是否完整
   - 確認軟連結或資料複製是否成功

### 日誌檢查
- 後端日誌：查看後端終端視窗
- 前端日誌：查看瀏覽器開發者工具 Console
- API 測試：執行 `python test_api.py`

## 版本資訊

- **版本**: 2.0.0
- **更新日期**: 2025年9月
- **部門**: BPM服務部
- **架構**: FastAPI + React
- **相容性**: 與原 Streamlit 版本資料完全相容

## 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 授權

此專案為 BPM服務部內部工具，僅供內部使用。
