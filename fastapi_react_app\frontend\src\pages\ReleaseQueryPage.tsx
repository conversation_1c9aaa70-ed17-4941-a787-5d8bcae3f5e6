import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tabs,
  Tab,
  Badge,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Assessment as AssessmentIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Code as CodeIcon,
  Folder as FolderIcon,
} from '@mui/icons-material';
import { releaseQueryAPI } from '../services/api';
import { ReleaseQueryResponse, ReleaseRecord } from '../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`release-tabpanel-${index}`}
      aria-labelledby={`release-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ReleaseQueryPage: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<string[]>([]);
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [branchTypes, setBranchTypes] = useState<string[]>([]);
  const [selectedBranchType, setSelectedBranchType] = useState<string>('');
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [searchFilename, setSearchFilename] = useState<string>('');
  const [searchResults, setSearchResults] = useState<ReleaseQueryResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [tabValue, setTabValue] = useState<number>(0);

  // 載入專案列表
  useEffect(() => {
    const loadProjects = async () => {
      try {
        const response = await releaseQueryAPI.getProjects();
        setProjects(response.data);
        if (response.data.length > 0) {
          setSelectedProject(response.data[0]);
        }
      } catch (err) {
        setError('無法載入專案列表');
        console.error('Error loading projects:', err);
      }
    };
    loadProjects();
  }, []);

  // 載入分支類型
  useEffect(() => {
    if (selectedProject) {
      const loadBranchTypes = async () => {
        try {
          const response = await releaseQueryAPI.getBranchTypes(selectedProject);
          setBranchTypes(response.data);
          if (response.data.length > 0) {
            setSelectedBranchType(response.data[0]);
          }
        } catch (err) {
          console.error('Error loading branch types:', err);
        }
      };
      loadBranchTypes();
    }
  }, [selectedProject]);

  // 處理關鍵字搜尋
  const handleKeywordSearch = async () => {
    if (!searchKeyword.trim() || !selectedProject) {
      setError('請輸入搜尋關鍵字並選擇專案');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await releaseQueryAPI.searchByKeyword(selectedProject, searchKeyword);
      setSearchResults(response.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || '關鍵字搜尋失敗');
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  // 處理檔案名稱搜尋
  const handleFilenameSearch = async () => {
    if (!searchFilename.trim() || !selectedProject) {
      setError('請輸入檔案名稱並選擇專案');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await releaseQueryAPI.searchByFilename(selectedProject, searchFilename);
      setSearchResults(response.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || '檔案名稱搜尋失敗');
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  // 渲染搜尋結果
  const renderSearchResults = (results: ReleaseQueryResponse) => {
    if (results.total_count === 0) {
      return (
        <Alert severity="info" sx={{ mt: 2 }}>
          找不到符合條件的記錄
        </Alert>
      );
    }

    // 按分支分組
    const groupedResults = results.records.reduce((acc, record) => {
      if (!acc[record.branch]) {
        acc[record.branch] = [];
      }
      acc[record.branch].push(record);
      return acc;
    }, {} as Record<string, ReleaseRecord[]>);

    return (
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          🔎 搜尋結果
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          共找到 {results.total_count} 筆記錄，分佈在 {Object.keys(groupedResults).length} 個分支中
        </Typography>

        {Object.entries(groupedResults).map(([branch, records]) => (
          <Card key={branch} sx={{ mt: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CodeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  分支: {branch}
                </Typography>
                <Badge badgeContent={records.length} color="primary" sx={{ ml: 2 }} />
              </Box>

              {records.map((record, index) => (
                <Accordion key={index} sx={{ mt: 1 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ width: '100%' }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                        {record.commit_message}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <PersonIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                          {record.author}
                        </Typography>
                        <ScheduleIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          {record.date}
                        </Typography>
                      </Box>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        <strong>Commit Hash:</strong> {record.commit_hash}
                      </Typography>
                      {record.files && record.files.length > 0 && (
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                            修改的檔案 ({record.files.length} 個):
                          </Typography>
                          <List dense>
                            {record.files.map((file, fileIndex) => (
                              <ListItem key={fileIndex} sx={{ py: 0.5 }}>
                                <FolderIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                                <ListItemText
                                  primary={file}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}
            </CardContent>
          </Card>
        ))}
      </Box>
    );
  };

  return (
    <Box>
      {/* 頁面標題 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          📊 產品Release記錄查詢工具
        </Typography>
      </Box>

      {/* 錯誤訊息 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* 專案選擇 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            選擇專案
          </Typography>
          <FormControl fullWidth>
            <InputLabel>專案</InputLabel>
            <Select
              value={selectedProject}
              label="專案"
              onChange={(e) => setSelectedProject(e.target.value)}
            >
              {projects.map((project) => (
                <MenuItem key={project} value={project}>
                  {project}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </CardContent>
      </Card>

      {/* 查詢方式選擇 */}
      <Card>
        <CardContent>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="關鍵字搜尋Commit訊息" />
            <Tab label="檔案名稱搜尋" />
            <Tab label="依照Branch名稱瀏覽" />
          </Tabs>

          {/* 關鍵字搜尋 */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  label="請輸入搜尋關鍵字"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleKeywordSearch()}
                  placeholder="例如：修正、新增、優化..."
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={handleKeywordSearch}
                  disabled={loading || !searchKeyword.trim() || !selectedProject}
                  startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                >
                  {loading ? '搜尋中...' : '搜尋'}
                </Button>
              </Grid>
            </Grid>
            {searchResults && renderSearchResults(searchResults)}
          </TabPanel>

          {/* 檔案名稱搜尋 */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  label="請輸入檔案名稱關鍵字"
                  value={searchFilename}
                  onChange={(e) => setSearchFilename(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleFilenameSearch()}
                  placeholder="例如：Action.java、Main.jsp..."
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={handleFilenameSearch}
                  disabled={loading || !searchFilename.trim() || !selectedProject}
                  startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                >
                  {loading ? '搜尋中...' : '搜尋'}
                </Button>
              </Grid>
            </Grid>
            {searchResults && renderSearchResults(searchResults)}
          </TabPanel>

          {/* 依照Branch名稱瀏覽 */}
          <TabPanel value={tabValue} index={2}>
            <Alert severity="info">
              此功能需要進一步開發，將提供分支瀏覽功能
            </Alert>
          </TabPanel>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ReleaseQueryPage;
