@echo off
setlocal enabledelayedexpansion
title BPM Easy Tools - FastAPI + React - Start Application

echo.
echo ==========================================
echo    BPM Easy Tools - FastAPI + React
echo    Start Application
echo ==========================================
echo.

REM 檢查是否存在虛擬環境
if not exist "..\venv" (
    echo Error: Virtual environment not found!
    echo Please run setup_environment.cmd in the parent directory first.
    pause
    exit /b 1
)

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8 or higher
    echo Download URL: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 檢查 Node.js 是否安裝
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js not found, please install Node.js 16 or higher
    echo Download URL: https://nodejs.org/
    pause
    exit /b 1
)

REM 檢查 npm 是否安裝
npm --version >nul 2>&1
if errorlevel 1 (
    echo Error: npm not found, please install Node.js with npm
    pause
    exit /b 1
)

echo Starting BPM Easy Tools (FastAPI + React)...
echo.

REM 獲取本機 IP 位址
set LOCAL_IP=
set FALLBACK_IP=
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set TEMP_IP=%%b
        REM 移除前導空格
        for /f "tokens=* delims= " %%c in ("!TEMP_IP!") do set TEMP_IP=%%c
        REM 跳過回環地址 (127.x.x.x)
        echo !TEMP_IP! | findstr /b "127\." >nul
        if errorlevel 1 (
            REM 優先選擇 10.x.x.x 網路（企業網路）
            echo !TEMP_IP! | findstr /b "10\." >nul
            if not errorlevel 1 (
                set LOCAL_IP=!TEMP_IP!
                goto :ip_found
            ) else (
                REM 如果沒有找到 10.x.x.x，保留作為備用
                if not defined FALLBACK_IP set FALLBACK_IP=!TEMP_IP!
            )
        )
    )
)
REM 如果沒有找到 10.x.x.x，使用備用 IP
if not defined LOCAL_IP set LOCAL_IP=%FALLBACK_IP%
:ip_found

REM 建立資料目錄軟連結（如果不存在）
if not exist "data_output" (
    echo Creating data_output symlink...
    mklink /D "data_output" "..\data_output"
    if errorlevel 1 (
        echo Warning: Failed to create symlink, copying data directory...
        xcopy "..\data_output" "data_output" /E /I /Q
    )
)

REM 安裝後端依賴（如果需要）
echo Checking backend dependencies...
cd backend
if not exist "venv" (
    echo Creating backend virtual environment...
    python -m venv venv
)

call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo Installing backend dependencies...
    pip install -r requirements.txt
)

REM 安裝前端依賴（如果需要）
echo Checking frontend dependencies...
cd ..\frontend
if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install
)

REM 啟動後端服務（背景執行）
echo Starting FastAPI backend server...
cd ..\backend
start "FastAPI Backend" cmd /c "call venv\Scripts\activate.bat && python main.py"

REM 等待後端啟動
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM 啟動前端服務
echo Starting React frontend server...
cd ..\frontend
start "React Frontend" cmd /c "npm start"

REM 等待前端啟動
echo Waiting for frontend to start...
timeout /t 10 /nobreak >nul

echo.
echo ==========================================
echo   Application Started Successfully!
echo ==========================================
echo.
echo Access URLs:
echo   Frontend (React):  http://localhost:3000
if defined LOCAL_IP (
    echo                     http://!LOCAL_IP!:3000
)
echo   Backend API:       http://localhost:8000
if defined LOCAL_IP (
    echo                     http://!LOCAL_IP!:8000
)
echo   API Documentation: http://localhost:8000/docs
echo.
echo Note: 
echo - The application is accessible from other devices on your network
echo - Frontend will automatically open in your default browser
echo - Press Ctrl+C in the respective terminal windows to stop services
echo.
echo Press any key to exit this launcher...
pause >nul
